# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Structure

This is a full-stack monorepo with two main applications:

1. **Frontend (Next.js)**: Root directory - Community website for Monad blockchain with documentation system
2. **Backend (Go/Gin)**: `gmonad/` directory - API server providing backend services

## Development Commands

### Frontend (Next.js - root directory)
```bash
npm run dev      # Start development server on localhost:3000
npm run build    # Build for production
npm run start    # Start production server
npm run lint     # Run ESLint
```

### Backend (Go - gmonad/ directory)
```bash
cd gmonad
go run main.go   # Start API server on port 8080
go test ./...    # Run all tests
go test ./utils  # Run specific package tests
go mod tidy      # Clean up dependencies
```

## Architecture Overview

### Frontend Architecture
- **Next.js 15** with React 19 and TypeScript
- **Multi-level documentation system**: Supports nested routing with `[...slug]` for hierarchical docs
- **Dynamic components**: LeftSidebar for navigation, RightSidebar for TOC generation
- **Content management**: Rich text editors (Quill, Vditor) with Cloudinary integration
- **Authentication**: NextAuth.js integration with backend JWT system

Key frontend patterns:
- Module CSS for styling with CSS modules
- Context API for authentication state
- Custom hooks for localStorage and document expansion
- Dynamic imports for heavy components

### Backend Architecture  
- **MVC Pattern**: Controllers, Models, Routes separation
- **Gin Framework**: HTTP routing with middleware stack
- **GORM + PostgreSQL**: Database operations with auto-migration
- **JWT Authentication**: Role-based access control
- **Background Jobs**: Timer-based monitoring tasks

### Documentation System
The `/docs` route supports multi-level nesting:
- Content in `src/docs/` with markdown files
- Automatic TOC generation from h2-h4 headings
- Chinese text support for heading IDs
- Sticky navigation sidebars

## Configuration

### Backend Configuration
- Requires `gmonad/config.yaml` (see `config.example.yaml`)
- Database: PostgreSQL connection
- JWT secret for authentication  
- OAuth provider settings
- RPC endpoints for Monad testnet
- Background job intervals

### Frontend Environment
- Uses NextAuth.js for authentication
- Cloudinary for image uploads
- API integration with backend at `/api/*`

## Key API Endpoints

Located in `gmonad/routes/routes.go`:
- `/v1/auth/*` - Authentication
- `/v1/users/*` - User management
- `/v1/events/*` - Event management
- `/v1/blogs/*` - Article/blog management
- `/v1/dapps/*` - DApp directory
- `/v1/tutorials/*` - Tutorial content
- `/v1/posts/*` - Community posts
- `/v1/feedbacks/*` - User feedback

## Testing

### Frontend
- No specific test framework configured
- Use TypeScript compilation to catch errors: `npx tsc --noEmit`

### Backend  
- Unit tests in `gmonad/utils/tool_test.go`
- Run with: `cd gmonad && go test ./utils`
- Full test suite: `cd gmonad && go test ./...`

## Development Workflow

1. **Frontend development**: Run `npm run dev` from root
2. **Backend development**: Run `go run main.go` from `gmonad/` directory  
3. **Database setup**: Configure PostgreSQL and update `gmonad/config.yaml`
4. **Content management**: Use admin interface for managing blogs, events, tutorials
5. **Documentation**: Add markdown files to `src/docs/` with proper frontmatter