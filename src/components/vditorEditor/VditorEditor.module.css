/* VditorEditor CSS!W */

.error {
  padding: 20px;
  text-align: center;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  background-color: #fff2f0;
}

.container {
  width: 100%;
  position: relative;
}

.editor {
  width: 100%;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(2px);
}

.loadingContent {
  padding: 30px;
  text-align: center;
  color: #666;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background-color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loadingContent::before {
  content: '';
  width: 24px;
  height: 24px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}