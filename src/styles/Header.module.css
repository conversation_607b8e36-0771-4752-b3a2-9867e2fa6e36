.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

/* Header */
.header {
  background: transparent;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 101;
  padding: 0.75rem 1rem 0;
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
  box-shadow: 0px 2px 4px rgba(226, 226, 226, 0.5);
  border-radius: 72px;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 72px;
}

.headerContent:hover {
  background: rgba(255, 255, 255, 0.65);
  box-shadow: 0px 6px 16px rgba(110, 84, 255, 0.15);
  transform: translateY(-1px);
}

.logoInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.logoTitle {
  font-size: 1.4rem;
  font-weight: 700;
  background: linear-gradient(135deg, #1f2937 0%, #6E54FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  white-space: nowrap;
  letter-spacing: -0.02em;
}

.nav {
  display: none;
  align-items: center;
  gap: 2rem;
  flex: 1;
  justify-content: flex-end;
  margin: 0 2.5rem;
}

.navItem {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #4b5563;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0.5rem 0.75rem;
  border-radius: 0.75rem;
  white-space: nowrap;
  position: relative;
}

.navItem:hover {
  color: #6E54FF;
  background: rgba(110, 84, 255, 0.1);
}

.navIcon {
  width: 1rem;
  height: 1rem;
}

.navButton {
  background: #6E54FF;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 2rem;
  font-size: 0.95rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(110, 84, 255, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  white-space: nowrap;
  flex-shrink: 0;
  letter-spacing: 0.01em;
}

.navButton:hover {
  background: #5b47d1;
  box-shadow: 0 4px 12px rgba(110, 84, 255, 0.4);
  transform: translateY(-1px);
}

.iconAlign {
  vertical-align: middle;
  margin-right: 4px;
}

.logo {
  border-radius: 4px;
}

/* Floating News Banner */
.floatingNewsBanner {
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  background: transparent;
  color: #1f2937;
  padding: 0.75rem 0;
  z-index: 90;
  overflow: hidden;
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
}

.floatingNewsBanner.hidden {
  opacity: 0;
  transform: translateY(-100%);
  pointer-events: none;
}

.newsSlider {
  display: flex;
  animation: slideNews 60s linear infinite;
  white-space: nowrap;
}

.newsSlide {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0 2rem;
  min-width: 100vw;
  justify-content: center;
}

.newsBadge {
  background: linear-gradient(135deg, #6E54FF, #6366F1);
  color: white;
  border: 1px solid rgba(124, 58, 237, 0.3);
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.3);
}

.newsText {
  font-size: 0.875rem;
  flex-shrink: 0;
  color: #1f2937;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.8);
}

@keyframes slideNews {
  0% {
    transform: translateX(0);
  }
  20% {
    transform: translateX(-100vw);
  }
  40% {
    transform: translateX(-200vw);
  }
  60% {
    transform: translateX(-300vw);
  }
  80% {
    transform: translateX(-400vw);
  }
  100% {
    transform: translateX(-500vw);
  }
}

.floatingNewsBanner:hover .newsSlider {
  animation-play-state: paused;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.8;
  }
}

/* 移动端导航菜单 */
.mobileNav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.mobileMenuButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: transparent;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mobileMenuButton:hover {
  background: #f3f4f6;
  border-color: #6E54FF;
  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.15);
}

.mobileMenuButton:active {
  transform: scale(0.95);
}

.mobileMenuIcon {
  width: 20px;
  height: 20px;
  color: #374151;
}

/* 移动端菜单内容样式 */
.mobileMenuContent {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.mobileMenuSection {
  margin-bottom: 0;
}

.mobileMenuSectionTitle {
  margin-bottom: 1rem;
  color: #1f2937;
  font-weight: 600;
  font-size: 1rem;
  position: relative;
  padding-bottom: 0.5rem;
}

.mobileMenuSectionTitle::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 2rem;
  height: 2px;
  background: linear-gradient(90deg, #6366F1, #6E54FF);
  border-radius: 1px;
}

.mobileMenuLinks {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobileMenuLink {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: #374151;
  text-decoration: none;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  background: transparent;
  border: 1px solid transparent;
}

.mobileMenuLink:hover {
  background: rgba(139, 92, 246, 0.05);
  border-color: rgba(139, 92, 246, 0.2);
  color: #6E54FF;
  transform: translateX(4px);
}

.mobileMenuLink span:first-child {
  font-size: 1.1rem;
  width: 1.5rem;
  text-align: center;
}

.mobileMenuLink span:last-child {
  font-weight: 500;
}

/* 移动端菜单响应式优化 */
@media (max-width: 480px) {
  .mobileMenuContent {
    gap: 1.5rem;
  }
  
  .mobileMenuLink {
    padding: 0.6rem 0.75rem;
    gap: 0.6rem;
  }
  
  .mobileMenuLink span:first-child {
    font-size: 1rem;
    width: 1.25rem;
  }
  
  .mobileMenuSectionTitle {
    font-size: 0.95rem;
  }
}

/* 移动端 Header 优化 */
@media (max-width: 768px) {
  .header {
    padding: 0.5rem 1rem 0;
  }
  
  .headerContent {
    max-width: calc(100% - 2rem);
    padding: 0.75rem 1.25rem;
    margin: 0 1rem;
    min-height: 60px;
  }
  
  .logoTitle {
    font-size: 1.2rem;
  }
  
  .navButton {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 0.375rem 0.5rem 0;
  }
  
  .headerContent {
    padding: 0.6rem 1rem;
    margin: 0 0.5rem;
    max-width: calc(100% - 1rem);
    min-height: 56px;
  }
  
  .logoTitle {
    font-size: 1.1rem;
  }
  
  .mobileMenuButton {
    width: 36px;
    height: 36px;
  }
  
  .mobileMenuIcon {
    width: 18px;
    height: 18px;
  }
  
  .navButton {
    padding: 0.5rem 0.8rem;
    font-size: 0.8rem;
  }
}

/* Responsive Design */
@media screen and (min-width: 768px) {
  .nav {
    display: flex;
  }
  
  .mobileNav {
    display: none;
  }
}
