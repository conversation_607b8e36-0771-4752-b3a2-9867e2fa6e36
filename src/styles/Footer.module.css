.container {
  max-width: 1200px;
  margin: 0 auto;
  /* padding: 0 1rem; */
}

/* Footer */
.footer {
  background: #1f2937;
  color: white;
  padding: 4rem 1rem;
}

/* 移动端 Footer 优化 */
@media (max-width: 768px) {
  .footer {
    padding: 2.5rem 1rem;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 2rem 0.75rem;
  }
}

/* .footerContent {
  display: grid;
  grid-template-columns: 2fr repeat(4, 1fr);
  gap: 0.5rem;
  margin-bottom: 2rem;
} */

.footerContent {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .footerContent {
    grid-template-columns: 2fr repeat(4, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
}

@media (max-width: 480px) {
  .footerContent {
    grid-template-columns: 1fr;
    gap: 1.25rem;
    margin-bottom: 1.5rem;
  }
}

.footerSection {
  flex: 1 1 150px;
  margin-bottom: 2rem;
}

.footerSection:first-child {
  flex: 2 1 220px;
}

@media (max-width: 768px) {
  .footerSection {
    flex: 1 1 100%;
    margin-bottom: 1.5rem;
  }

  .footerSection:first-child {
    flex: 1 1 100%;
    text-align: center;
  }
}

.footerLogo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.footerLogoIcon {
  position: relative;
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, #6E54FF, #6366F1);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footerLogoText {
  color: white;
  font-weight: bold;
  font-size: 0.875rem;
}

.footerLogoGlow {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #6E54FF, #6366F1);
  border-radius: 0.5rem;
  filter: blur(10px);
  opacity: 0.5;
}

.footerLogoTitle {
  font-size: 1.25rem;
  font-weight: bold;
}

.footerDescription {
  color: #9ca3af;
  line-height: 1.6;
}

.footerSectionTitle {
  font-weight: 600;
  margin-bottom: 1.5rem;
}

/* 移动端标题优化 */
@media (max-width: 768px) {
  .footerSectionTitle {
    margin-bottom: 1rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .footerSectionTitle {
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
  }
}

.logo {
  border-radius: 4px;
}

.footerLinks {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.footerLinks li {
  margin-bottom: 0;
}

/* 移动端链接横向排列 */
@media (max-width: 768px) {
  .footerLinks {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem 1rem;
  }
}

@media (max-width: 480px) {
  .footerLinks {
    grid-template-columns: 1fr 1fr;
    gap: 0.4rem 0.8rem;
  }
}

.footerLink {
  color: #9ca3af;
  text-decoration: none;
  transition: color 0.3s ease;
  font-size: 0.9rem;
}

/* 移动端链接文字优化 */
@media (max-width: 480px) {
  .footerLink {
    font-size: 0.85rem;
  }
}

.footerLink:hover {
  color: white;
}

.footerSocial {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-start;
  align-items: center;
}

/* 移动端社交媒体按钮优化 */
@media (max-width: 768px) {
  .footerSocial {
    justify-content: center;
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .footerSocial {
    gap: 0.6rem;
  }
}

.socialButton {
  background: transparent;
  border: 1px solid #4b5563;
  color: #9ca3af;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 移动端社交按钮优化 */
@media (max-width: 480px) {
  .socialButton {
    width: 2.25rem;
    height: 2.25rem;
    padding: 0;
  }

  .socialIcon {
    width: 1rem;
    height: 1rem;
  }
}

.socialButton:hover {
  background: #374151;
  color: white;
  transform: scale(1.05);
  border-color: #6b7280;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.socialIcon {
  width: 1.125rem;
  height: 1.125rem;
  flex-shrink: 0;
}

.footerBottom {
  border-top: 1px solid #374151;
  padding-top: 2rem;
  text-align: center;
}

/* 移动端底部优化 */
@media (max-width: 768px) {
  .footerBottom {
    padding-top: 1.5rem;
  }
}

@media (max-width: 480px) {
  .footerBottom {
    padding-top: 1.25rem;
  }
}

.footerCopyright {
  color: #9ca3af;
  margin: 0;
}

/* 移动端版权文字优化 */
@media (max-width: 768px) {
  .footerCopyright {
    font-size: 0.875rem;
    line-height: 1.5;
  }
}

@media (max-width: 480px) {
  .footerCopyright {
    font-size: 0.8rem;
  }
}

.toOpenBuild {
  color: #6E54FF;
}

.toOpenBuild:hover {
  color: #6E54FF;
}

/* Responsive Design */
@media screen and (min-width: 768px) {
  .footerContent {
    grid-template-columns: repeat(2, 1fr);
    gap: 2.5rem;
  }

  .footerSection:first-child {
    grid-column: 1 / -1;
    text-align: left;
    margin-bottom: 2rem;
  }
}

@media screen and (min-width: 1024px) {
  .footerContent {
    grid-template-columns: repeat(4, 1fr);
    gap: 3rem;
  }

  .footerSection:first-child {
    grid-column: 1;
    text-align: left;
    margin-bottom: 0;
  }
}