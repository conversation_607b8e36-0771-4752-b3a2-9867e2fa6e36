/* marked解析markdown文档，外包裹器样式 */
@import url(./markedContainer.css); 

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #6e54ff;
  --primary-light: #6366f1;
  --primary-dark: #4338ca;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

.ql-container {
  min-height: 150px;
}

.ql-editor {
  min-height: 150px;
  cursor: text;
}

.ql-editor p {
  min-height: 1em;
}

/* 处理vditor编辑器全屏层级低于header头 */
.vditor--fullscreen{
  z-index: 110!important; 
}

/* 配合浮动导航栏加一个上内边距样式类 */
.nav-t-top {
  padding-top: 6rem!important;
}
 