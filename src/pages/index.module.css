/* 基础布局 */
.homepage {
  min-height: 100vh;
  background: #f8fafc;
  color: #1f2937;
  position: relative;
  overflow-x: hidden;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  /* padding: 0 1rem; */
}

/* Header */
.header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(24px);
  border-bottom: 1px solid rgba(229, 231, 235, 0.8);
  position: fixed;
  /* 改为fixed确保始终可见 */
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  /* 提高z-index确保在最顶层 */
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logoIcon {
  position: relative;
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(135deg, #6E54FF, #6366F1);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 25px rgba(124, 58, 237, 0.25);
}

.logoText {
  color: white;
  font-weight: bold;
  font-size: 1.125rem;
}

.logoGlow {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #6E54FF, #6366F1);
  border-radius: 0.75rem;
  filter: blur(10px);
  opacity: 0.5;
  animation: pulse 3s infinite;
}

.logoTitle {
  font-size: 1.5rem;
  font-weight: bold;
  background: linear-gradient(135deg, #1f2937, #6E54FF);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.nav {
  display: none;
  align-items: center;
  gap: 2rem;
}

.navItem {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
}

.navItem:hover {
  color: #6E54FF;
  transform: scale(1.05);
}

.navIcon {
  width: 1rem;
  height: 1rem;
}

.navButton {
  background: linear-gradient(135deg, #6E54FF, #6366F1);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  box-shadow: 0 10px 25px rgba(124, 58, 237, 0.25);
  transition: all 0.3s ease;
  cursor: pointer;
}

.navButton:hover {
  box-shadow: 0 15px 35px rgba(124, 58, 237, 0.4);
  transform: scale(1.05);
}

/* Floating News Banner */
.floatingNewsBanner {
  position: fixed;
  top: 80px;
  /* 调整到 header 下面 */
  left: 0;
  right: 0;
  background: transparent;
  /* 完全透明背景 */
  color: #1f2937;
  padding: 0.75rem 0;
  z-index: 90;
  /* 确保在header下面但在其他内容上面 */
  overflow: hidden;
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
  /* 添加平滑过渡 */
}

.floatingNewsBanner.hidden {
  opacity: 0;
  transform: translateY(-100%);
  pointer-events: none;
}

.newsSlider {
  display: flex;
  animation: slideNews 60s linear infinite;
  /* 从40s增加到60s，更慢 */
  white-space: nowrap;
}

.newsSlide {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0 2rem;
  min-width: 100vw;
  justify-content: center;
}

.newsBadge {
  background: linear-gradient(135deg, #6E54FF, #6366F1);
  color: white;
  border: 1px solid rgba(124, 58, 237, 0.3);
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.3);
  /* 给标签添加轻微阴影以增强可读性 */
}

.newsText {
  font-size: 0.875rem;
  flex-shrink: 0;
  color: #1f2937;
  font-weight: 600;
  /* 增加字重以提高可读性 */
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.8);
  /* 添加白色文字阴影以增强可读性 */
}

@keyframes slideNews {
  0% {
    transform: translateX(0);
  }

  20% {
    transform: translateX(-100vw);
  }

  40% {
    transform: translateX(-200vw);
  }

  60% {
    transform: translateX(-300vw);
  }

  80% {
    transform: translateX(-400vw);
  }

  100% {
    transform: translateX(-500vw);
  }
}

.floatingNewsBanner:hover .newsSlider {
  animation-play-state: paused;
}

/* Hero Section */
.hero {
  position: relative;
  padding: 4rem 1rem;
  padding-top: 5.5rem;
  /* 为固定导航栏自然留出空间 */
  overflow: hidden;
  background: transparent;
}

/* 移动端Hero优化 */
@media (max-width: 768px) {
  .hero {
    padding: 3rem 1rem;
    padding-top: 4.5rem;
  }

  .heroTitle {
    font-size: clamp(2rem, 8vw, 2.5rem);
    margin-bottom: 1rem;
  }

  .heroButtons {
    flex-direction: column;
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 2rem 0.5rem;
    padding-top: 3rem;
  }

  .heroTitle {
    font-size: clamp(1.8rem, 8vw, 2.2rem);
    margin-bottom: 0.75rem;
  }

  .heroContent {
    padding: 0 0.5rem;
  }

  .heroSubtitle {
    margin-bottom: 2rem;
  }

  .heroGallery {
    margin: 2rem 0 1.5rem;
  }
}

.heroBackground {
  position: absolute;
  inset: 0;
  z-index: 0;
}

.heroGradient {
  position: absolute;
  inset: 0;
  background-image: url('/welcome-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.mouseGradient {
  position: absolute;
  inset: 0;
  opacity: 0.3;
  pointer-events: none;
}

.particles {
  position: absolute;
  inset: 0;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #6366F1;
  border-radius: 50%;
  opacity: 0.2;
  animation: pulse 2s infinite;
}

.heroContent {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  text-align: center;
  opacity: 0;
  transform: translateY(2.5rem);
  transition: all 1s ease;
  width: 100%;
  padding: 2rem 1rem;
}

.heroVisible {
  opacity: 1;
  transform: translateY(0);
}

.heroBadge {
  display: inline-block;
  background: rgba(110, 84, 255, 0.15);
  border: 1px solid rgba(110, 84, 255, 0.3);
  color: #6E54FF;
  padding: 0.5rem 1rem;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
}

.heroTitle {
  width: 546px;
  height: 172px;
  font-family: 'Noto Sans SC', sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 72px;
  line-height: 86px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #0E100F;
  margin: 0 auto;
  margin-bottom: 0;
  position: relative;
  z-index: 2;
}

@media (max-width: 1200px) {
  .heroTitle {
    height: auto !important;
    min-height: unset !important;
  }
}

/* 1024px-1200px之间的优化：增加导航栏空间并调整标题装饰间距 */
@media (min-width: 1025px) and (max-width: 1200px) {
  .hero {
    padding-top: 6.5rem;
  }

  .heroTitle {
    transform: translateY(-30px);
  }

  .titleDecoration {
    margin-top: -1rem;
    margin-bottom: 2.5rem;
  }
}


.heroTitlePrimary {
  background: linear-gradient(135deg, #1f2937, #6E54FF, #3b82f6);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.heroTitleSecondary {
  color: inherit;
}

/* 标题装饰 */
.titleDecoration {
  position: relative;
  width: 546px;
  height: 80px;
  margin: 0 auto;
  margin-bottom: 2rem;
  margin-top: -6rem;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.decorationGradient {
  position: absolute;
  width: 546px;
  height: 80px;
  left: 0;
  top: 0;
  background: linear-gradient(180deg, rgba(110, 84, 255, 0) 0%, #6E54FF 100%);
  opacity: 0.5;
}

.decorationLine {
  position: absolute;
  width: 546px;
  height: 8px;
  left: 0;
  top: 72px;
  background: #6E54FF;
}

.heroSubtitle {
  width: 775px;
  height: 80px;
  font-family: 'Noto Sans SC', sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 40px;
  display: flex;
  align-items: center;
  text-align: center;
  color: #0E100F;
  margin: 0 auto;
  margin-bottom: 1rem;
  justify-content: center;
  max-width: 775px;
  margin-top: -2rem;
}

.heroHighlight {
  color: #0E100F;
  font-weight: 500;
}

.heroButtons {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  margin-bottom: 0.5rem;
}

.heroPrimaryButton {
  background: #6E54FF;
  color: white !important;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 2rem;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(110, 84, 255, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  backdrop-filter: blur(10px);
  text-decoration: none;
}

.heroPrimaryButton:hover {
  background: #5b47d1;
  color: white !important;
  box-shadow: 0 6px 16px rgba(110, 84, 255, 0.4);
  transform: translateY(-1px);
  border: none;
  text-decoration: none;
}

.heroSecondaryButton {
  background: rgba(255, 255, 255, 0.9);
  color: #6E54FF;
  border: 1px solid rgba(110, 84, 255, 0.2);
  padding: 0.75rem 1.5rem;
  border-radius: 2rem;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(110, 84, 255, 0.15);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  backdrop-filter: blur(10px);
}

.heroSecondaryButton:hover {
  background: rgba(255, 255, 255, 1);
  border-color: #6E54FF;
  box-shadow: 0 6px 16px rgba(110, 84, 255, 0.25);
  transform: translateY(-1px);
  color: #5b47d1;
}

.buttonIcon {
  width: 1.5rem;
  height: 1.5rem;
  color: inherit;
  transition: all 0.3s ease;
}

.buttonIconX {
  width: 1rem;
  height: 1rem;
  color: inherit;
  transition: all 0.3s ease;
}

.heroPrimaryButton .buttonIcon {
  color: white;
}

.heroPrimaryButton:hover .buttonIcon {
  color: white;
}

.heroSecondaryButton .buttonIcon {
  color: #6E54FF;
}

.heroSecondaryButton:hover .buttonIcon {
  color: #5b47d1;
}

/* Hero Gallery */
.heroGallery {
  margin: 0.5rem 0 1.5rem;
  width: 100%;
  overflow: visible;
  position: relative;
  padding: 0 2rem;
}

/* 只在非触摸设备（桌面端）显示导航按钮 */
@media (hover: hover) and (pointer: fine) {
  .heroGallery:hover .galleryNavigation {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }

  .galleryNavigation:hover {
    opacity: 1 !important;
    transform: translateY(-50%) scale(1.1) !important;
  }
}

.galleryContainer {
  display: flex;
  gap: 1.5rem;
  justify-content: flex-start;
  align-items: center;
  overflow-x: auto;
  scroll-behavior: smooth;
  padding: 1.5rem 1rem;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
  width: 100%;
  margin: 0 auto;
  position: relative;
  cursor: grab;
}

.galleryContainer::-webkit-scrollbar {
  display: none;
}

.galleryContainer:active {
  cursor: grabbing;
}

/* PC端鼠标滚轮支持 */
.galleryContainer {
  scroll-snap-type: x mandatory;
  scroll-padding-left: 0;
}

/* 桌面端优化显示 */
@media (min-width: 1024px) {
  .heroContent {
    max-width: 1400px;
    padding: 1.5rem 0;
  }

  .heroGallery {
    padding: 0;
    overflow: hidden;
    margin: 0.25rem 0 1rem;
  }

  .galleryContainer {
    max-width: none;
    margin: 0 auto;
    justify-content: flex-start;
    padding: 1.25rem 3rem;
    overflow-x: auto;
    gap: 2rem;
  }

  .galleryImage {
    min-width: 280px;
  }

  .galleryImage img {
    width: 280px;
    height: 182px;
  }

  .heroButtons {
    margin-bottom: 0.25rem;
  }
}

.galleryImage {
  scroll-snap-align: center;
}

/* Gallery Navigation Buttons */
.galleryNavigation {
  position: absolute;
  top: 50%;
  transform: translateY(-50%) scale(0.8);
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(16px);
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  opacity: 0;
  pointer-events: auto;
}

.galleryNavigation:hover {
  background: rgba(110, 84, 255, 0.1);
  border-color: rgba(110, 84, 255, 0.3);
  box-shadow: 0 12px 32px rgba(110, 84, 255, 0.2);
}

.galleryNavPrev {
  left: 20px;
}

.galleryNavNext {
  right: 20px;
}

.galleryNavIcon {
  width: 20px;
  height: 20px;
  color: #6E54FF;
  transition: all 0.3s ease;
}

.galleryNavigation:hover .galleryNavIcon {
  color: #5b47d1;
}

/* 在触摸设备上隐藏导航按钮 */
@media (hover: none) and (pointer: coarse) {
  .galleryNavigation {
    display: none !important;
  }
}

.galleryImage {
  flex: 0 0 auto;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(16px);
  border: 2px solid rgba(255, 255, 255, 0.8);
  position: relative;
  opacity: 1;
  min-width: 300px;
}

.galleryImage:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  border-color: rgba(0, 0, 0, 0.15);
}

.galleryImage img {
  width: 300px;
  height: 195px;
  object-fit: cover;
  display: block;
  border-radius: 14px;
  transition: all 0.3s ease;
}


/* Welcome Section 响应式设计 */
@media (max-width: 1024px) {
  .heroTitle {
    width: 100%;
    max-width: 500px;
    font-size: 60px;
    line-height: 72px;
    height: auto;
    min-height: unset;
    margin-bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    transform: translateY(60px);
  }

  .titleDecoration {
    width: 100%;
    max-width: 500px;
    margin-top: -1rem;
  }

  .decorationGradient {
    width: 100%;
    max-width: 500px;
  }

  .decorationLine {
    width: 100%;
    max-width: 500px;
  }

  .heroSubtitle {
    width: 100%;
    max-width: 600px;
    font-size: 20px;
    line-height: 32px;
    height: auto;
    padding: 0 1rem;
    margin-top: -1.5rem;
    margin-bottom: 0.75rem;
  }
}

@media (max-width: 768px) {
  .heroContent {
    padding: 1.5rem 1rem;
  }

  .heroTitle {
    font-size: 48px !important;
    line-height: 56px !important;
    max-width: 400px;
    width: 100%;
    margin-bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    height: auto !important;
    min-height: unset !important;
    position: relative;
    transform: translateY(60px) !important;
  }

  .titleDecoration {
    max-width: 400px;
    width: 100%;
    margin-top: 0rem !important;
  }

  .decorationGradient {
    width: 100%;
  }

  .decorationLine {
    width: 100%;
  }

  .heroSubtitle {
    font-size: 18px;
    line-height: 28px;
    max-width: 500px;
    width: 100%;
    margin-top: -1.2rem;
    margin-bottom: 0.5rem;
  }

  .heroGallery {
    margin: 0.25rem 0 1.25rem;
  }

  .galleryContainer {
    justify-content: flex-start;
    padding: 1.5rem 1rem;
    gap: 1rem;
    max-width: none;
    scroll-padding-left: 1rem;
  }

  .galleryImage {
    min-width: 200px;
  }

  .galleryImage img {
    width: 200px;
    height: 130px;
  }


  .heroButtons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .heroPrimaryButton,
  .heroSecondaryButton {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }
}

@media (max-width: 640px) {
  .heroTitle {
    transform: translateY(70px) !important;
  }

  .titleDecoration {
    margin-top: 0rem !important;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 36px !important;
    line-height: 56px !important;
    max-width: 300px;
    width: 100%;
    margin-bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    height: auto !important;
    min-height: unset !important;
    position: relative;
    transform: translateY(60px) !important;
  }

  .titleDecoration {
    max-width: 300px;
    width: 100%;
    margin-top: 0rem !important;
  }

  .heroSubtitle {
    font-size: 16px;
    line-height: 24px;
    max-width: 320px;
    width: 100%;
    margin-top: -1rem;
  }

  .heroGallery {
    margin: 0rem 0 1rem;
  }

  .galleryContainer {
    padding: 1rem 0.5rem;
    gap: 0.5rem;
    justify-content: flex-start;
    scroll-padding-left: 0.5rem;
  }

  .galleryImage {
    min-width: 160px;
  }

  .galleryImage img {
    width: 160px;
    height: 104px;
  }

  .heroButtons {
    gap: 0.5rem;
  }

  .heroPrimaryButton,
  .heroSecondaryButton {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    max-width: 250px;
  }
}

/* Stats Section */
.stats {
  padding: 4rem 1rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.statItem {
  text-align: center;
  transition: all 0.3s ease;
}

.statItem:hover {
  transform: translateY(-5px);
}

.statIconWrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 1rem;
}

.statIconGlow {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg,
      rgba(168, 85, 247, 0.2),
      rgba(59, 130, 246, 0.2));
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  filter: blur(20px);
}

.statItem:hover .statIconGlow {
  opacity: 1;
}

.statIconContainer {
  position: relative;
  display: inline-flex;
  padding: 0.75rem;
  border-radius: 50%;
  background: #f3e8ff;
  color: #6E54FF;
  transition: transform 0.3s ease;
}

.statItem:hover .statIconContainer {
  transform: scale(1.1);
}

.statIcon {
  width: 1.5rem;
  height: 1.5rem;
}

.statValue {
  font-size: 1.875rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.statItem:hover .statValue {
  color: #6E54FF;
}

.statLabel {
  color: #6b7280;
}

/* Section Headers */
.sectionHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 1rem;
}

.sectionDescription {
  font-size: 1.25rem;
  color: #6b7280;
  max-width: 48rem;
  margin: 0 auto;
  line-height: 1.6;
}

.sectionFooter {
  text-align: center;
  margin-top: 3rem;
}

/* Features Section */
.features {
  padding: 5rem 1rem;
  background: #f8fafc;
}

.featuresGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

.featureCard {
  position: relative;
  transition: all 0.3s ease;
}

.featureCardGlow {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg,
      rgba(110, 84, 255, 0.08),
      rgba(99, 102, 241, 0.06));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 1rem;
  filter: blur(20px);
}

.featureCard:hover .featureCardGlow {
  opacity: 0.15;
}

.featureCardContent {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(229, 231, 235, 0.6);
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.featureCard:hover .featureCardContent {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border-color: rgba(110, 84, 255, 0.15);
}

.featureIconWrapper {
  display: inline-flex;
  padding: 1rem;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(110, 84, 255, 0.1), rgba(99, 102, 241, 0.08));
  color: #6E54FF;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.featureCard:hover .featureIconWrapper {
  transform: scale(1.03);
  background: linear-gradient(135deg, rgba(110, 84, 255, 0.15), rgba(99, 102, 241, 0.12));
}

.featureIcon {
  width: 2rem;
  height: 2rem;
}

.featureTitle {
  font-size: 1.25rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.featureDescription {
  color: #6b7280;
  line-height: 1.6;
}

/* Activities Section */
.activities {
  padding: 5rem 1rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.activitiesGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.activityCard {
  position: relative;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(229, 231, 235, 0.8);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.5s ease;
}

.activityCardGlow {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg,
      rgba(168, 85, 247, 0.2),
      rgba(59, 130, 246, 0.2));
  opacity: 0;
  transition: opacity 0.5s ease;
  border-radius: 1rem;
  filter: blur(20px);
}

.activityCard:hover .activityCardGlow {
  opacity: 0.2;
}

.activityCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  border-color: rgba(110, 84, 255, 0.2);
}

.activityCardHeader {
  margin-bottom: 1.5rem;
}

.activityMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.activityBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
}

.activityBadgeActive {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.activityBadgeInactive {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.activityParticipants {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.activityIcon {
  width: 1rem;
  height: 1rem;
}

.activityTitle {
  font-size: 1.25rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.activityDescription {
  color: #6b7280;
  line-height: 1.6;
}

.activityCardContent {
  margin-top: auto;
}

.activityInfo {
  margin-bottom: 1.5rem;
}

.activityInfoItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
}

.activityButton {
  width: 100%;
  background: #374151;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.activityButton:hover {
  background: #374151;
  transform: translateY(-1px);
}

.moreButton {
  background: transparent;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.moreButton:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

/* Milestones Section */
.milestones {
  padding: 5rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.timeline {
  position: relative;
  max-width: 64rem;
  margin: 0 auto;
}

.timelineLine {
  position: absolute;
  left: 2rem;
  top: 0;
  bottom: 0;
  width: 5px;
  background: linear-gradient(to bottom,
      #6E54FF 0%,
      #6366F1 35%,
      #8B5CF6 65%,
      #a78bfa 100%);
  border-radius: 3px;
  box-shadow: 0 0 16px rgba(110, 84, 255, 0.25);
}

@media (min-width: 768px) {
  .timelineLine {
    left: 50%;
    transform: translateX(-50%);
  }
}

.milestoneItem {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 4rem;
  flex-direction: row;
}

@media (min-width: 768px) {
  .milestoneLeft {
    flex-direction: row;
  }

  .milestoneRight {
    flex-direction: row-reverse;
  }
}

.milestoneContent {
  flex: 1;
  margin-left: 6rem;
  padding: 1.5rem 0;
  position: relative;
}

@media (min-width: 768px) {
  .milestoneLeft .milestoneContent {
    margin-left: 0;
    margin-right: 4rem;
    text-align: right;
  }

  .milestoneRight .milestoneContent {
    margin-left: 4rem;
    margin-right: 0;
    text-align: left;
  }
}

.milestoneDate {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;
}

@media (min-width: 768px) {
  .milestoneLeft .milestoneDate {
    justify-content: flex-end;
  }

  .milestoneRight .milestoneDate {
    justify-content: flex-start;
  }
}

.milestoneDateBadge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #6E54FF;
  font-weight: 600;
  font-size: 0.875rem;
  background: linear-gradient(135deg, rgba(110, 84, 255, 0.08), rgba(99, 102, 241, 0.06));
  padding: 0.4rem 1rem;
  border-radius: 24px;
  border: 1px solid rgba(110, 84, 255, 0.15);
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(110, 84, 255, 0.08);
}

.milestoneDateIcon {
  width: 1rem;
  height: 1rem;
}

.milestoneTitle {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.8rem;
  transition: all 0.3s ease;
  letter-spacing: -0.01em;
  line-height: 1.3;
  position: relative;
  z-index: 2;
}

.milestoneContent:hover .milestoneTitle {
  color: #6E54FF;
}

.milestoneContent:hover .milestoneDescription {
  color: #374151;
}

.milestoneDescription {
  color: #6b7280;
  line-height: 1.65;
  font-size: 1rem;
  max-width: 420px;
  margin: 0 auto;
  transition: all 0.3s ease;
  letter-spacing: 0.01em;
  position: relative;
  z-index: 2;
}

/* 根据左右布局分别设置描述文字对齐 */
.milestoneLeft .milestoneDescription {
  text-align: right;
  margin-left: auto;
  margin-right: 0;
}

.milestoneRight .milestoneDescription {
  text-align: left;
  margin-left: 0;
  margin-right: auto;
}

.milestoneIcon {
  position: absolute;
  left: 2rem;
  transform: translateX(-50%);
  width: 4.2rem;
  height: 4.2rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #6E54FF 0%, #6366F1 50%, #8B5CF6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  box-shadow:
    0 8px 16px rgba(110, 84, 255, 0.15),
    0 2px 8px rgba(110, 84, 255, 0.1),
    inset 0 1px 3px rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
  border: 2px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
}

@media (min-width: 768px) {
  .milestoneIcon {
    left: 50%;
  }
}

.milestoneIconContent {
  position: relative;
  display: flex;
  z-index: 2;
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
}

.milestoneIconGlow {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  background: linear-gradient(135deg, #6E54FF, #a78bfa);
  filter: blur(10px);
  opacity: 0.5;
  animation: pulse 3s infinite;
}

.milestoneItem:hover .milestoneIcon {
  transform: translateX(-50%) scale(1.05);
  box-shadow:
    0 12px 24px rgba(110, 84, 255, 0.2),
    0 4px 12px rgba(110, 84, 255, 0.15),
    inset 0 2px 4px rgba(255, 255, 255, 0.3);
}

.milestoneItem:hover .milestoneIconContent {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transform: scale(1.02);
}

.milestoneDateBadge:hover {
  background: linear-gradient(135deg, rgba(110, 84, 255, 0.12), rgba(99, 102, 241, 0.1));
  border-color: rgba(110, 84, 255, 0.25);
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(110, 84, 255, 0.12);
}

@media (max-width: 767px) {
  .milestoneItem {
    flex-direction: row !important;
  }

  .milestoneContent {
    margin-left: 6rem !important;
    margin-right: 0 !important;
  }

  .milestoneContent::before {
    left: -4rem !important;
  }

  .milestoneDate {
    justify-content: flex-start !important;
  }

  .timelineLine {
    left: 2rem;
  }

  .milestoneIcon {
    left: 2rem;
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
  }
}

/* Resources Section */
.resources {
  padding: 5rem 1rem;
  background: #f8fafc;
}

.resourcesGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

.resourceCard {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(229, 231, 235, 0.6);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.resourceCardGlow {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg,
      rgba(110, 84, 255, 0.08),
      rgba(99, 102, 241, 0.06));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 1rem;
  filter: blur(20px);
}

.resourceCard:hover .resourceCardGlow {
  opacity: 0.15;
}

.resourceCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border-color: rgba(110, 84, 255, 0.15);
}

.resourceCardHeader {
  text-align: center;
  margin-bottom: 1.5rem;
}

.resourceIconWrapper {
  display: inline-flex;
  padding: 1rem;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(110, 84, 255, 0.1), rgba(99, 102, 241, 0.08));
  color: #6E54FF;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.resourceCard:hover .resourceIconWrapper {
  transform: scale(1.03);
  background: linear-gradient(135deg, rgba(110, 84, 255, 0.15), rgba(99, 102, 241, 0.12));
}

.resourceIcon {
  width: 1.5rem;
  height: 1.5rem;
}

.resourceTitle {
  font-size: 1.25rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.resourceDescription {
  color: #6b7280;
  line-height: 1.6;
}

.resourceCardFooter {
  padding-top: 0;
}

.resourceButton {
  width: 100%;
  background: linear-gradient(135deg, #6E54FF, #6366F1);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.resourceButton:hover {
  background: linear-gradient(135deg, #5b47d1, #4f46e5);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(110, 84, 255, 0.2);
}

/* Members Section */
.members {
  padding: 5rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.membersContainer {
  position: relative;
  overflow: hidden;
}

.membersGradientLeft {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 5rem;
  background: linear-gradient(to right, #f8fafc, transparent);
  z-index: 10;
}

.membersGradientRight {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 5rem;
  background: linear-gradient(to left, #f8fafc, transparent);
  z-index: 10;
}


.membersScrollStatic {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 2rem;
  overflow-x: hidden;
}

.membersScrollAuto {
  display: flex;
  gap: 2rem;
  overflow-x: auto;
  white-space: nowrap;
}

.membersScrollStatic:hover,
.membersScrollAuto:hover {
  animation-play-state: paused;
}

.memberItem a {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}

.avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  overflow: hidden;
  border-radius: 50%;
  cursor: pointer;
  border: 1.5px solid #e5e7eb;
  /* 更细更浅的灰色边框 */
  background: #fff;
  box-shadow: none;
  transition:
    border-color 0.2s,
    box-shadow 0.2s;
}

.avatar:hover {
  border-color: #d1d5db;
  box-shadow:
    0 4px 16px rgba(100, 116, 139, 0.15),
    0 2px 8px rgba(31, 41, 55, 0.08);
  transform: scale(1.05);
  transition: all 0.3s ease;
}

.avatar :global(.ant-avatar) {
  width: 60px !important;
  height: 60px !important;
  line-height: 60px !important;
}

.memberName {
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.memberTwitter {
  margin-top: 10px;
  color: #6b7280;
  font-size: 1rem;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.memberItem:hover .memberName {
  color: #6E54FF;
}

.memberItem:hover .memberTwitter {
  color: #6366F1;
}

/* CTA Section */
.cta {
  padding: 5rem 1rem;
  background: linear-gradient(135deg, #6E54FF, #6366F1);
  color: white;
  position: relative;
  overflow: hidden;
}

.ctaBackground {
  position: absolute;
  inset: 0;
}

.ctaParticle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: white;
  border-radius: 50%;
  opacity: 0.2;
  animation: pulse 2s infinite;
}

.ctaContent {
  position: relative;
  z-index: 10;
  text-align: center;
  max-width: 64rem;
  margin: 0 auto;
}

.ctaTitle {
  font-size: clamp(2rem, 5vw, 3rem);
  font-weight: bold;
  margin-bottom: 1.5rem;
}

.ctaSubtitle {
  font-size: 1.25rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  line-height: 1.6;
}

.ctaButtons {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  justify-content: center;
  align-items: center;
}

.ctaPrimaryButton {
  background: white;
  color: #6E54FF;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ctaPrimaryButton:hover {
  background: #f3f4f6;
  color: #6E54FF;
  transform: scale(1.1);
}

.ctaSecondaryButton {
  background: transparent;
  color: white;
  border: 1px solid white;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ctaSecondaryButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: scale(1.1);
}

/* Footer */
.footer {
  background: #1f2937;
  color: white;
  padding: 4rem 1rem;
}

.footerContent {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.footerSection {
  margin-bottom: 2rem;
}

.footerLogo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.footerLogoIcon {
  position: relative;
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, #6E54FF, #6366F1);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footerLogoText {
  color: white;
  font-weight: bold;
  font-size: 0.875rem;
}

.footerLogoGlow {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #6E54FF, #6366F1);
  border-radius: 0.5rem;
  filter: blur(10px);
  opacity: 0.5;
}

.footerLogoTitle {
  font-size: 1.25rem;
  font-weight: bold;
}

.footerDescription {
  color: #9ca3af;
  line-height: 1.6;
}

.footerSectionTitle {
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.footerLinks {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footerLinks li {
  margin-bottom: 0.75rem;
}

.footerLink {
  color: #9ca3af;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footerLink:hover {
  color: white;
}

.footerSocial {
  display: flex;
  gap: 1rem;
}

.socialButton {
  background: transparent;
  border: 1px solid #4b5563;
  color: #9ca3af;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.socialButton:hover {
  background: #374151;
  color: white;
  transform: scale(1.1);
}

.socialIcon {
  width: 1rem;
  height: 1rem;
}

.footerBottom {
  border-top: 1px solid #374151;
  padding-top: 2rem;
  text-align: center;
}

.footerCopyright {
  color: #9ca3af;
  margin: 0;
}

/* Animations */
@keyframes pulse {

  0%,
  100% {
    opacity: 0.2;
  }

  50% {
    opacity: 0.8;
  }
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-50%);
  }
}

/* Responsive Design */
@media screen and (min-width: 640px) {
  .heroButtons {
    flex-direction: row;
  }

  .ctaButtons {
    flex-direction: row;
  }

  .statsGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media screen and (min-width: 768px) {
  .nav {
    display: flex;
  }

  .featuresGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .activitiesGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .resourcesGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .footerContent {
    grid-template-columns: repeat(2, 1fr);
  }

  .milestoneRight .milestoneContent {
    margin-left: 0;
    margin-right: 5rem;
  }
}

@media screen and (min-width: 1024px) {
  .featuresGrid {
    grid-template-columns: repeat(4, 1fr);
  }

  .activitiesGrid {
    grid-template-columns: repeat(3, 1fr);
  }

  .resourcesGrid {
    grid-template-columns: repeat(4, 1fr);
  }

  .footerContent {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media screen and (max-width: 996px) {
  .timelineLine {
    left: 1rem;
  }

  .milestoneItem {
    flex-direction: row !important;
    margin-bottom: 2rem;
  }

  .milestoneContent {
    margin-left: 3rem !important;
    margin-right: 0 !important;
  }

  .milestoneIcon {
    left: 1rem;
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
  }
}


.dappShowcase {
  padding: 5rem 1rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.dappsScrollContainer {
  display: flex;
  overflow-x: auto;
  gap: 1.5rem;
  padding: 2rem 1rem;
  scroll-behavior: smooth;
}

.dappsScrollContainer::-webkit-scrollbar {
  display: none;
}

.dappCard {
  flex: 0 0 auto;
  width: 300px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  background: #fff;
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.2s ease;
}

.dappCard:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

.coverContainer {
  position: relative;
  width: 100%;
  height: 140px;
  overflow: hidden;
}

.coverImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cardTop {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
}

.cardActions {
  display: flex;
  gap: 6px;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  background: rgba(0, 0, 0, 0.4);
  padding: 4px;
  border-radius: 6px;
}

.actionIcon {
  font-size: 20px;
}

.featuredBadge {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #facc15;
  padding: 4px;
  border-radius: 6px;
}

.featuredIcon {
  width: 16px;
  height: 16px;
  color: #000;
}

.logoContainer {
  position: relative;
  margin-left: 16px;
  margin-top: -32px;
  width: 64px;
  height: 64px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.cardContent {
  padding: 16px;
  flex-grow: 1;
}

.dappName {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #1e293b;
}

.dappDescription {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.4;
  margin-bottom: 0.5rem;
  height: 2.5rem;
  overflow: hidden;
}

.category {
  margin-top: 0.25rem;
}

.tag {
  color: #6366F1;
  padding: 2px 6px;
  border-color: #6366F1;
  border-radius: 4px;
  font-size: 12px;
}

.cardFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #e5e7eb;
}

.viewMoreWrapper {
  margin-top: 2rem;
  text-align: center;
}

.viewMoreButton {
  display: inline-block;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: #fff;
  padding: 0.75rem 2rem;
  border-radius: 9999px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
}

.viewMoreButton:hover {
  background: linear-gradient(135deg, #5b47d1 0%, #6E54FF 100%);
  color: #fff;
  box-shadow: 0 4px 12px rgba(110, 84, 255, 0.3);
  transform: translateY(-1px);
}