.container {
  min-height: 100vh;
  background: linear-gradient(to bottom right, #faf5ff, #e1dbfe);
  padding: 2rem 1rem;
}

.header {
  padding: 3rem 0 2rem;
  text-align: left;
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #111827;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 1rem;
}

.titleIcon {
  color: #6366f1;
}

.subtitle {
  font-size: 1.1rem;
  color: #6b7280;
  max-width: 600px;
  margin: 0;
  line-height: 1.6;
}

.addTutorialButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #8b5cf6;
  color: white;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s;
}

.addTutorialButton:hover {
  background-color: #7c3aed;
  color: white;
  transform: translateY(-1px);
}

.addIcon {
  width: 1rem;
  height: 1rem;
}

.content {
  max-width: 1200px;
  margin: 0 auto;
}

.filters {
  margin-bottom: 2rem;
  border-radius: 16px !important;
}

.searchBox {
  margin-bottom: 1.5rem;
}

.searchInput {
  border-radius: 12px !important;
}

.filterGroup {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  align-items: center;
}

.filterItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filterIcon {
  color: #6366f1;
}

.filterLabel {
  font-weight: 600;
  color: #374151;
  white-space: nowrap;
  font-size: 0.9rem;
}

.select {
  min-width: 120px;
}

.loading {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: 1rem;
}

.tutorialGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.tutorialLink {
  text-decoration: none;
  color: inherit;
}

.tutorialCard {
  border-radius: 16px !important;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
}

.tutorialCard:hover {
  transform: translateY(-4px);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

.cardImage {
  position: relative;
  height: 110px;
  width: 100%;
  overflow: hidden;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.tutorialCard:hover .image {
  transform: scale(1.05);
}

.cardOverlay {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
}

.duration {
  background: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  border: none !important;
  border-radius: 12px !important;
  font-weight: 600;
}

.cardContent {
  padding: 0;
}

.cardHeader {
  margin-bottom: 1rem;
}

.cardTitle {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  line-height: 1.4;
  color: #111827;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.cardMeta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.dappName {
  color: #6366f1;
  font-size: 0.85rem;
  font-weight: 600;
}

.difficulty {
  font-weight: 600 !important;
  border-radius: 12px !important;
}

.cardDescription {
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 0.9rem;
}

.cardFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  flex: 1;
}

.tag {
  border-radius: 12px !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  transition: all 0.3s ease;
}

.tag:hover {
  transform: translateY(-1px);
}

.category {
  border-radius: 12px !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  white-space: nowrap;
}

.emptyState {
  text-align: center;
  padding: 4rem 2rem;
}

.emptyState h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #374151;
  font-weight: 600;
}

.emptyState p {
  font-size: 1rem;
  color: #6b7280;
}

.paginationContainer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 3rem;
  padding: 2rem 0;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .title {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .content {
    padding: 0 1rem 3rem;
  }

  .filterGroup {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .filterItem {
    justify-content: space-between;
  }

  .select {
    width: 100%;
    min-width: auto;
  }

  .tutorialGrid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  .cardFooter {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .cardMeta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .headerContent {
    padding: 0 1rem;
  }

  .title {
    font-size: 1.75rem;
  }

  .tutorialGrid {
    grid-template-columns: 1fr;
  }
}

/* Ant Design 组件样式覆盖 */
.ant-card-body {
  padding: 2rem;
}

.ant-card-cover {
  margin: 0;
}

.ant-input-affix-wrapper {
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.ant-select-selector {
  border-radius: 8px !important;
  border: 2px solid #e5e7eb !important;
  transition: all 0.3s ease;
}

.ant-select-focused .ant-select-selector {
  border-color: #6366f1 !important;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1) !important;
}

/* 分页器样式 */
.ant-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.ant-pagination-item {
  border-radius: 8px !important;
  border: 2px solid #e5e7eb !important;
  transition: all 0.3s ease;
}

.ant-pagination-item:hover {
  border-color: #6366f1 !important;
}

.ant-pagination-item-active {
  background-color: #6366f1 !important;
  border-color: #6366f1 !important;
}

.ant-pagination-item-active a {
  color: white !important;
}

.ant-pagination-prev,
.ant-pagination-next {
  border-radius: 8px !important;
  border: 2px solid #e5e7eb !important;
  transition: all 0.3s ease;
}

.ant-pagination-prev:hover,
.ant-pagination-next:hover {
  border-color: #6366f1 !important;
}

.ant-pagination-total-text {
  margin-right: 1rem;
  color: #6b7280;
  font-size: 0.9rem;
}

.ant-pagination-jump-prev,
.ant-pagination-jump-next {
  border-radius: 8px !important;
}

/* 响应式分页器 */
@media (max-width: 768px) {
  .paginationContainer {
    margin-top: 2rem;
    padding: 1rem 0;
    justify-content: center;
  }

  .ant-pagination {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .ant-pagination-total-text {
    margin-right: 0;
    margin-bottom: 0.5rem;
    width: 100%;
    text-align: center;
  }
}