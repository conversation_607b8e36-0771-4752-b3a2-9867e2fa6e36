.container {
  min-height: 100vh;
  background: linear-gradient(to bottom right, #faf5ff, #e1dbfe);
}

/* Header Section */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem 2rem 3rem;
  color: white;
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
}

.backButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: 500;
  font-size: 20px;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

.backButton:hover {
  color: white;
  transform: translateX(-2px);
}

.backIcon {
  width: 1.25rem;
  height: 1.25rem;
}

.dappInfo {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 2rem;
}

.dappHeader {
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;
}

.dappLogo {
  width: 80px;
  height: 80px;
  border-radius: 1rem;
  background: rgba(255, 255, 255, 0.1);
  object-fit: cover;
}

.dappDetails {
  flex: 1;
}

.dappName {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: white;
}

.dappDescription {
  font-size: 1.125rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.dappMeta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.category {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.xLink {
  display: flex;
  color: white;
  align-items: center;
  justify-content: center;
  margin-left: 20px;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  /* font-size: 0.875rem; */
}

.xLink:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
}

.websiteLink {
  display: flex;
  color: white;
  text-decoration: none;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  padding: 0.5rem 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  /* font-size: 0.875rem; */
}

.websiteLink:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
}

.actionIcon {
  font-size: 1rem;
}

.loading {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    gap: 1rem;
}

.loadingSpinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #6366f1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Tutorials Section */
.tutorialsSection {
  padding: 3rem 2rem;
}

.sectionContainer {
  max-width: 1200px;
  margin: 0 auto;
}

.tutorialsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.tutorialsTitle {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tutorialsCount {
  font-size: 1.25rem;
  color: #64748b;
  font-weight: 400;
}

.difficultyFilters {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.difficultyButton {
  padding: 0.5rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 1rem;
  background: white;
  color: #64748b;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.difficultyButton:hover {
  border-color: #6366F1;
  color: #6366F1;
}

.difficultyButton.active {
  color: white;
}

/* Tutorials Grid */
.tutorialsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.tutorialCard {
  background: white;
  border-radius: 1rem;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tutorialCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border-color: #6366F1;
}

.cardHeader {
  padding: 1.5rem 1.5rem 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.tutorialNumber {
  background: linear-gradient(135deg, #6366F1, #6E54FF);
  color: white;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.125rem;
}

.cardActions {
  display: flex;
  gap: 0.5rem;
}

.difficultyBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
}

.cardContent {
  padding: 1.5rem;
}

.tutorialTitle {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.75rem;
}

.tutorialDescription {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.tutorialMeta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.metaItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.875rem;
}

.metaIcon {
  width: 1rem;
  height: 1rem;
}

.cardFooter {
  padding: 1rem 1.5rem;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.startButton {
  width: 100%;
  background: linear-gradient(135deg, #6366F1, #6E54FF);
  color: white;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.startButton:hover {
  background: linear-gradient(135deg, #6E54FF, #4338CA);
  transform: translateY(-1px);
}

.startIcon {
  width: 1rem;
  height: 1rem;
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 4rem 2rem;
  color: #64748b;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.emptyTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.emptyDescription {
  font-size: 1rem;
  max-width: 400px;
  margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .tutorialsGrid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 1.5rem 1rem 2rem;
  }

  .dappName {
    font-size: 1.5rem;
  }

  .dappDescription {
    font-size: 1rem;
  }

  .dappHeader {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .dappMeta {
    justify-content: center;
  }

  .tutorialsSection {
    padding: 2rem 1rem;
  }

  .tutorialsHeader {
    flex-direction: column;
    align-items: flex-start;
  }

  .tutorialsTitle {
    font-size: 1.5rem;
  }

  .tutorialsGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .dappInfo {
    padding: 1.5rem;
  }

  .tutorialCard {
    margin: 0 -0.5rem;
  }

  .difficultyFilters {
    width: 100%;
    justify-content: flex-start;
  }

  .difficultyButton {
    flex: 1;
    min-width: 0;
    text-align: center;
  }
}