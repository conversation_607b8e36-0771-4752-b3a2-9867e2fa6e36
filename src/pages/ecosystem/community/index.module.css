.container {
  min-height: 100vh;
  padding: 1rem 0.5rem 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f9fafb;
}

.main {
  padding: 5rem 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.title {
  margin: 0;
  line-height: 1.15;
  font-size: 2.5rem;
  text-align: center;
  color: #1f2937;
}

.title a {
  color: #6e54ff;
  text-decoration: none;
}

.title a:hover,
.title a:focus,
.title a:active {
  text-decoration: underline;
}

.description {
  text-align: center;
  margin: 2rem 0 4rem;
  line-height: 1.5;
  font-size: 1.25rem;
  color: #4b5563;
  max-width: 800px;
  padding: 0 1rem;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.card {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  text-align: left;
  color: inherit;
  text-decoration: none;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card:hover,
.card:focus,
.card:active {
  color: #6e54ff;
  border-color: #6e54ff;
  transform: translateY(-5px);
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.card h2 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  color: #1f2937;
}

.card p {
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
  line-height: 1.5;
  color: #4b5563;
  flex-grow: 1;
}

.category {
  display: inline-block;
  background-color: #f3e8ff;
  color: #6e54ff;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  align-self: flex-start;
  margin-bottom: 1rem;
}

.githubIcon {
  color: #6b7280;
  width: 20px;
  height: 20px;
  transition: color 0.2s ease;
  margin-left: auto;
  margin-top: auto;
}

.githubIcon:hover {
  color: #6e54ff;
}

.card {
  position: relative;
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  text-align: left;
  color: inherit;
  text-decoration: none;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card a[href*='github'] {
  position: absolute;
  bottom: 1.5rem;
  right: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #f3f4f6;
  transition: all 0.2s ease;
}

.card a[href*='github']:hover {
  background-color: #e5e7eb;
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
    max-width: 500px;
  }

  .title {
    font-size: 2rem;
  }

  .description {
    font-size: 1.1rem;
  }
}
