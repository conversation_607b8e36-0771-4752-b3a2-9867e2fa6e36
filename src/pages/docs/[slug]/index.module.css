.container {
  background-color: #f9fafb;
}
.layout {
  display: grid;
  grid-template-columns: 250px 1fr 250px;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 0.5rem;
  align-items: start;
}
.leftSidebar {
  position: sticky;
  top: 7rem;
  max-height: calc(100vh - 6rem);
  overflow-y: auto;
}
.rightSidebar {
  position: sticky;
  top: 7rem; 
  max-height: calc(100vh - 6rem);
  overflow-y: auto;
}
.mainContent {
  min-width: 0;
}
/* 面包屑导航样式 */
.breadcrumbWrapper {
  padding: 0.5rem 0.5rem 0;
  background-color: #fff;
  border-radius: 0.5rem;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.breadcrumbList {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0.5rem;
}
.breadcrumbItem {
  display: flex;
  align-items: center;
}
.breadcrumbLink {
  color: #8b5cf6;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  transition: color 0.2s;
}
.breadcrumbLink:hover {
  color: #7c3aed;
}
.breadcrumbCurrent {
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
}
.breadcrumbSeparator {
  display: flex;
  align-items: center;
  color: #9ca3af;
}
.homeIcon {
  font-size: 1rem;
}
.separatorIcon {
  font-size: 1.125rem;
  font-weight: 300;
}
/* 左右侧边栏滚动条样式 */
.leftSidebar::-webkit-scrollbar,
.rightSidebar::-webkit-scrollbar {
  width: 4px;
}
.leftSidebar::-webkit-scrollbar-track,
.rightSidebar::-webkit-scrollbar-track {
  background: transparent;
}
.leftSidebar::-webkit-scrollbar-thumb,
.rightSidebar::-webkit-scrollbar-thumb {
  background: #e2e8f0;
  border-radius: 2px;
}
.leftSidebar::-webkit-scrollbar-thumb:hover,
.rightSidebar::-webkit-scrollbar-thumb:hover {
  background: #cbd5e1;
}
/* Firefox 左右侧边栏滚动条样式 */
.leftSidebar,
.rightSidebar {
  scrollbar-width: thin;
  scrollbar-color: #e2e8f0 transparent;
}
.paper {
  background-color: white;
  border-radius: 0.5rem;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 2rem;
  padding-top: 0.5rem;
}
.prose {
  max-width: none;
  line-height: 1.75;
  color: #374151;
}
.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: #111827;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
}
.prose h1 {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.prose h2 {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.prose h3 {
  font-size: 1.5rem;
  line-height: 2rem;
}
.prose p {
  margin-bottom: 1.25rem;
  color: #374151;
}
.prose code {
  color: #ec4899;
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}
.prose pre {
  background-color: #111827;
  color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.25rem 0;
}
.prose pre code {
  background-color: transparent;
  color: inherit;
  padding: 0;
}
.prose blockquote {
  border-left: 4px solid #8b5cf6;
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: #6b7280;
}
.prose ul,
.prose ol {
  margin: 1.25rem 0;
  padding-left: 1.5rem;
}
.prose li {
  margin: 0.5rem 0;
}
.prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
}
.prose th,
.prose td {
  border: 1px solid #d1d5db;
  padding: 0.75rem;
  text-align: left;
}
.prose th {
  background-color: #f9fafb;
  font-weight: 600;
}
.prose a {
  color: #8b5cf6;
  text-decoration: none;
}
.prose a:hover {
  color: #1d4ed8;
  text-decoration: underline;
}
.prose img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1.5rem 0;
}
.prose strong {
  font-weight: 600;
  color: #111827;
}
.prose em {
  font-style: italic;
}
/* 侧边栏样式 */
.sidebarContent {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  border-left: 4px solid #8b5cf6;
}
.sidebarTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 1rem 0;
}
/* 左侧文档导航 */
.docNav {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}
/* 分类组样式 */
.categoryGroup {
  display: flex;
  flex-direction: column;
}
.categoryHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.75rem 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
}
.categoryHeader:hover {
  background-color: #f1f5f9;
}
.categoryTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  text-align: left;
}
.categoryIcon {
  width: 1rem;
  height: 1rem;
  color: #6b7280;
  transition: transform 0.2s;
}
/* 分类下的文档列表 */
.categoryDocs {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  margin-left: 0.75rem;
  margin-top: 0.25rem;
  margin-bottom: 0.5rem;
}
.docLink {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  color: #374151;
  text-decoration: none;
  font-weight: 400;
  transition: all 0.2s;
  font-size: 0.875rem;
  border-radius: 0.375rem;
}
.docLink:hover {
  color: #8b5cf6;
  background-color: #f8fafc;
}
.docLinkActive {
  color: #8b5cf6;
  font-weight: 500;
  background-color: #ede9fe;
}
.docTitle {
  flex: 1;
}
.docArrow {
  width: 0.875rem;
  height: 0.875rem;
  color: #9ca3af;
  margin-left: 0.5rem;
}
/* 右侧目录导航 */
.tocNav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.tocItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}
.tocLink {
  text-align: left;
  padding: 0.5rem 0;
  color: #374151;
  text-decoration: none;
  font-weight: 500;
  border: none;
  background: none;
  cursor: pointer;
  transition: color 0.2s;
  font-size: 0.875rem;
}

.tocLink[data-level="2"] {
  padding-left: 0;
  font-weight: 500;
}

.tocLink[data-level="3"] {
  padding-left: 1rem;
  font-weight: 400;
  font-size: 0.8125rem;
}

.tocLink[data-level="4"] {
  padding-left: 2rem;
  font-weight: 400;
  font-size: 0.75rem;
  color: #6b7280;
}
.tocLink:hover {
  color: #8b5cf6;
}
.tocChildren {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-left: 1rem;
}
.tocChildLink {
  text-align: left;
  padding: 0.25rem 0;
  color: #6b7280;
  text-decoration: none;
  font-weight: 400;
  border: none;
  background: none;
  cursor: pointer;
  transition: color 0.2s;
  font-size: 0.8125rem;
}
.tocChildLink:hover {
  color: #a855f7;
}
/* 1800px+宽屏适配 */
@media (min-width: 1800px) {
  .layout {
    max-width: 1500px;
    grid-template-columns: 320px 1fr 320px;
  }
}
/* 1920x1080分辨率适配 */
@media (min-width: 1920px) {
  .layout {
    max-width: 1600px;
    grid-template-columns: 340px 1fr 340px;
  }
}
/* 响应式设计 */
@media (max-width: 768px) {
  .layout {
    display: block;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  .leftSidebar,
  .rightSidebar {
    position: static;
    width: auto;
    max-height: none;
    margin-bottom: 1.5rem;
  }
  .sidebarContent {
    padding: 1rem;
  }
  .docNav {
    flex-direction: column;
    gap: 0.5rem;
  }
  .categoryDocs {
    margin-left: 0;
  }
  .categoryHeader {
    padding: 0.5rem;
  }
  .tocNav {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;
  }
  .tocItem {
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
  }
  .tocChildren {
    margin-left: 0;
    flex-direction: row;
    gap: 0.5rem;
  }
}
