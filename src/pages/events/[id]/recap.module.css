.container {
  min-height: 100vh;
  background: linear-gradient(to bottom right, #faf5ff, #dbeafe);
  padding: 2rem 0;
}

.content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.1rem;
  color: #6b7280;
}

/* 返回按钮 */
.backButton {
  margin-bottom: 2rem;
}

.backBtn {
  color: #8b5cf6 !important;
  border: none !important;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  font-size: 20px;
  gap: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.backBtn:hover {
  background-color: #f3f4f6 !important;
  color: #7c3aed !important;
}

/* 活动信息概览 */
.eventOverview {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.eventInfo {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

.eventImage {
  flex-shrink: 0;
  width: 200px;
  height: 120px;
  border-radius: 0.75rem;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.eventImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.eventDetails {
  flex: 1;
}

.eventTitle {
  font-size: 1.75rem;
  font-weight: 700;
  color: #111827;
  margin: 0 0 0.75rem 0;
  line-height: 1.3;
}

.eventDescription {
  color: #555;
  font-size: 14px;
  line-height: 1.6;
  margin: 0 0 1rem 0;
}

.eventMeta {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.metaItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.9rem;
}

.metaItem svg {
  color: #8b5cf6;
}

/* 表单标题 */
.formTitle {
  font-size: 1.75rem;
  font-weight: 700;
  color: #111827;
  margin: 0 0 2rem 0;
  text-align: flex-start;
}

/* 表单布局 */
.form {
  max-width: none;
}

.formLayout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  align-items: start;
}

.leftColumn {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.rightColumn {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sectionTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #f3f4f6;
}

/* 表单项样式覆盖 */
.ant-form-item-label > label {
  font-weight: 600;
  color: #374151;
  font-size: 1rem;
}

.ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
  color: #ef4444;
}

.textarea {
  border-radius: 0.5rem !important;
  border: 1px solid #d1d5db !important;
  font-size: 1rem !important;
  line-height: 1.6 !important;
  resize: vertical !important;
  font-family: inherit !important;
}

.textarea:focus {
  border-color: #8b5cf6 !important;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1) !important;
}

.input {
  border-radius: 0.5rem !important;
  border: 1px solid #d1d5db !important;
  font-size: 1rem !important;
  height: 2.75rem !important;
}

.input:focus {
  border-color: #8b5cf6 !important;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1) !important;
}

.ant-input-prefix {
  color: #8b5cf6;
}

/* 媒体提示 */
.mediaNote {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 1rem;
}

.mediaNote p {
  font-weight: 600;
  color: #475569;
  margin: 0 0 0.5rem 0;
}

.mediaNote ul {
  margin: 0;
  padding-left: 1.25rem;
  color: #64748b;
  font-size: 0.9rem;
}

.mediaNote li {
  margin-bottom: 0.25rem;
}

/* 提交按钮 */
.submitSection {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.submitButton {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed) !important;
  border: none !important;
  border-radius: 0.5rem !important;
  height: 3rem !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
  transition: all 0.2s ease !important;
}

.submitButton:hover {
  background: linear-gradient(135deg, #7c3aed, #6d28d9) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3) !important;
}

.submitButton:active {
  transform: translateY(0) !important;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .formLayout {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .rightColumn {
    order: -1;
  }
}

@media (max-width: 768px) {
  .content {
    padding: 0 1rem;
  }

  .eventOverview {
    padding: 1.5rem;
  }

  .eventInfo {
    flex-direction: column;
    gap: 1.5rem;
  }

  .eventImage {
    width: 100%;
    height: 160px;
  }

  .eventTitle {
    font-size: 1.5rem;
  }

  .eventMeta {
    gap: 1rem;
  }

  .formTitle {
    font-size: 1.5rem;
  }

  .leftColumn,
  .formSection,
  .submitSection {
    padding: 1.25rem;
  }

  .sectionTitle {
    font-size: 1.125rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 1rem 0;
  }

  .eventOverview {
    padding: 1rem;
  }

  .leftColumn,
  .formSection,
  .submitSection {
    padding: 1rem;
  }

  .eventMeta {
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
  }

  .formTitle {
    font-size: 1.25rem;
  }
}
