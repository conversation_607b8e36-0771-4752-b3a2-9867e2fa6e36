/* 容器和布局 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f3e8ff 0%, #dbeafe 100%);
}

.content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* 页面标题 */
.header {
  margin-bottom: 2rem;
}

.titleSection {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.titleIcon {
  width: 2.5rem;
  height: 2.5rem;
  background-color: #8b5cf6;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.titleIconSvg {
  width: 1.5rem;
  height: 1.5rem;
  color: white;
}

.title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.subtitle {
  color: #6b7280;
  margin: 0;
}

/* 控制区域 */
.controlSection {
  margin-bottom: 2rem;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.dateDisplay {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dateIcon {
  width: 1rem;
  height: 1rem;
  color: #6b7280;
}

.dateText {
  font-size: 0.875rem;
  color: #6b7280;
}

/* 日历选择器 */
.calendarPicker {
  position: relative;
}

.calendarButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.calendarButton:hover {
  background-color: #f9fafb;
}

.calendarIcon {
  width: 1rem;
  height: 1rem;
  color: #6b7280;
}

.calendarButtonText {
  font-size: 0.875rem;
  font-weight: 500;
}

.calendarChevron {
  width: 1rem;
  height: 1rem;
  color: #6b7280;
  transition: transform 0.2s;
}

.calendarChevronOpen {
  transform: rotate(180deg);
}

.calendarDropdown {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 0.5rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 50;
  padding: 1rem;
  min-width: 20rem;
}

.calendarQuickSelect {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.quickSelectButton {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  background-color: #f3f4f6;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.quickSelectButton:hover {
  background-color: #e5e7eb;
}

.calendarNavigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.navButton {
  padding: 0.25rem;
  background: none;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.navButton:hover {
  background-color: #f3f4f6;
}

.navIcon {
  width: 1rem;
  height: 1rem;
}

.monthTitle {
  font-weight: 500;
  margin: 0;
}

.calendarWeekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.weekday {
  text-align: center;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  padding: 0.5rem;
}

.calendarDays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.25rem;
}

.dayButton {
  position: relative;
  padding: 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  background: none;
  color: #111827;
}

.dayButton:hover {
  background-color: #f3f4f6;
}

.dayButtonInactive {
  color: #d1d5db;
}

.dayButtonToday {
  background-color: #ede9fe;
  color: #7c3aed;
  font-weight: 600;
}

.dayButtonSelected {
  background-color: #8b5cf6;
  color: white;
}

/* 统计卡片网格 */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* 统计卡片 */
.statsCard {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
  transition: box-shadow 0.2s ease;
}

.statsCard:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.cardHeader {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.cardIcon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cardIconSvg {
  width: 1.5rem;
  height: 1.5rem;
  color: white;
}

.cardTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.cardTotal {
  margin-bottom: 1rem;
}

.totalNumber {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.totalLabel {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.cardNew {
  margin-bottom: 1rem;
}

.newNumber {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.newLabel {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.cardGrowth {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.trendIcon {
  width: 1rem;
  height: 1rem;
  color: #10b981;
}

.growthText {
  font-size: 0.875rem;
  font-weight: 500;
}

.growthText.positive {
  color: #059669;
}

.growthText.negative {
  color: #dc2626;
}

.growthLabel {
  font-size: 0.875rem;
  color: #6b7280;
}

/* 图表区域 */
.chartSection {
  margin-bottom: 2rem;
}

.chartContainer {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
}

.chartHeader {
  margin-bottom: 1.5rem;
}

.chartTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 0.5rem 0;
}

.chartLegend {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.legendItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legendColor {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
}

.legendLabel {
  font-size: 0.875rem;
  color: #6b7280;
}

.chartWrapper {
  position: relative;
}

.chart {
  width: 100%;
  height: 320px;
}

/* 加载和错误状态 */
.loadingContainer,
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.spinner {
  width: 3rem;
  height: 3rem;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #8b5cf6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingText,
.errorText {
  color: #6b7280;
  margin-bottom: 1rem;
}

.retryButton {
  padding: 0.5rem 1rem;
  background-color: #8b5cf6;
  color: white;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retryButton:hover {
  background-color: #7c3aed;
}

/* 响应式设计 */
@media (min-width: 640px) {
  .controls {
    flex-direction: row;
    align-items: center;
  }
}

@media (min-width: 768px) {
  .content {
    padding: 2rem;
  }
  
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .statsGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .statsGrid {
    grid-template-columns: repeat(5, 1fr);
  }
}

@media (max-width: 640px) {
  .title {
    font-size: 1.5rem;
  }
  
  .statsCard {
    padding: 1rem;
  }
  
  .chartContainer {
    padding: 1rem;
  }
  
  .chartLegend {
    gap: 0.5rem;
  }
  
  .legendLabel {
    font-size: 0.75rem;
  }
}
