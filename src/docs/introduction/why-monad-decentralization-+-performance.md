# 为什么选择 Monad: 去中心化+性能

### 去中心化很重要

区块链有几个主要组成部分：

* **共识机制**，用于就添加到分类账的交易确认达成一致。
* **执行/存储系统**，用于维持区块链活动状态。

在提高这些组件的性能时，可以偷工减料，例如要求所有节点在物理上相互靠近（以节省达成共识的开销），或要求大量使用 RAM（以将大部分或全部状态缓存在内存中），但这是以牺牲去中心化为代价的。

而去中心化正是关键所在！

正如”[为什么是区块链](https://docs.monad.xyz/technical-discussion/why-blockchain)“章节中所讨论的，去中心化的全局状态共享允许多方协调，同时依赖于单一、共享、客观的真实消息源。去中心化是问题的关键，由一小群节点操作员（或在极端情况下，一个操作员！）维护的区块链将无法拥有去信任化、可信的中立性和抗审查性等特征。

对于任何区块链网络来说，去中心化都应该是主要关注点，提高性能不应以牺牲去中心化为代价。

### 当下区块链性能瓶颈

以太坊当前的执行限制（1.25M Gas/每秒）设置得比较保守，这样做有几个理由：

* 低效的存储访问模式
* 单线程执行
* 执行时间预算非常有限，因为没有执行就无法达成共识
* 对状态增长的担忧，以及状态增长对未来状态访问成本的影响

Monad 通过改进算法和架构来解决这些局限性，开创了多项创新，有望在未来几年成为以太坊的标准。保持高度去中心化，同时进行实质性的性能优化，这是关键的考虑因素。

### 通过优化解决瓶颈问题

Monad 在以下四个主要方面实现了流水线作业并有其他优化，从而使以太坊虚拟机具有卓越的性能，极大地推动了区块链的去中心化和可扩展性博弈的有效边界。下文将介绍这些主要方面的优化：

* [MonadBFT 共识机制](../monad-architecture/consensus/monadbft)
* [延迟执行（Deferred Execution）](../monad-architecture/consensus/deferred-execution)
* [并行执行（Parallel Execution）](../monad-architecture/execution/parallel-execution)
* [MonadDb 数据库](../monad-architecture/execution/monaddb)
