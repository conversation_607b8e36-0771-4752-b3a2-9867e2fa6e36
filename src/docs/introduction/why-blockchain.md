# 为什么是区块链？

区块链是不同参与者之间就两件事达成的去中心化协议：

1. 交易的官方**排序（分类账）**
2. **世界**官方状况，包括账户余额和各种程序的状态。

在以太坊等现代区块链中，交易包括余额转账、新程序的创建以及对现有程序的函数调用。迄今为止所有交易的汇总结果构成了当前状态，因此，*对上述 (1) 达成一致必然意味着对 (2) 达成一致。*

区块链系统有一组协议规则，也称为共识机制，它描述了当前同步的分布式节点如何相互通信，以就添加到账本的附加交易达成一致。（[MonadBFT](https://docs.monad.xyz/monad-arch/consensus/monad-bft)就是共识机制的一个例子。）

归纳使节点保持同步：它们从相同的状态开始并应用相同的交易，因此在应用新的交易列表结束时，它们仍然保持一致的状态。

共享全局状态支持开发去中心化应用——这些应用“存在于区块链上”，即存在于区块链系统的每个节点上。去中心化应用是一段代码（以及持久化的特定于应用的状态），任何用户都可以调用它，只需提交指向该应用某个函数的交易即可。区块链中的每个节点都负责正确执行被调用的字节码；复制机制确保每个节点的诚实性。

## 示例

去中心化应用可以实现我们原本期望以中心化方式实现的功能。例如，一个非常简单的去中心化应用示例是*虚拟银行*（在加密货币中通常称为借贷协议）。

在现实世界中，银行是一家吸收存款并以较高利率贷出资金的企业。银行赚取高利率和低利率之间的差额；借款人获得贷款，用于开展经济生产活动；而您则从存款中赚取利息。每个人都是赢家！

虚拟银行其实就是一个包含四种主要方法的应用程序：`deposit`、`withdraw`、`borrow`和`repay`。每种方法的逻辑主要是簿记，以确保正确跟踪存款和贷款：

```python
class VirtualBank:
  def deposit(sender, amount):
    # transfer amount from sender to myself (the bank)
    # do internal bookkeeping to credit the sender


  def withdraw(sender, amount):
    # ensure the sender had enough on deposit
    # do internal bookkeeping to debit the sender
    # transfer amount from myself (the bank) to sender


  def borrow(sender, amount):
    # ...


  def repay(sender, amount);
    # ...
```



在以太坊或 Monad 中，有人可以为这个虚拟银行编写代码并上传；然后任何人都可以利用它进行借贷，这可能比尝试在其本国获取银行服务要容易得多。

这个简单的例子展示了去中心化应用的强大功能。以下是一些其他值得一提的优势：

- **开放 API/可组合性**：分散式应用程序可以被其他分散式应用程序原子调用，从而允许开发人员通过堆叠现有组件来构建更复杂的功能。
- **透明性**：应用程序逻辑纯粹通过代码表达，因此任何人都可以审查逻辑的副作用。状态透明且可审计；DeFi 中的储备证明是默认设置。
- **抗审查和可信中立：**任何人都可以向无需许可的网络提交交易或上传应用程序。
- **全球覆盖**：任何可以访问互联网的人都可以访问重要的金融服务，包括没有银行账户或银行账户不足的用户。
