# Asynchronous I/O

Asynchronous I/O 是一种输入/输出处理方式，它允许中央处理器在通信进行时继续并发执行。

硬盘和网络比中央处理器慢几个数量级，与启动输入/输出操作并等待结果相比，中央处理器可以在需要数据时立即启动输入/输出操作，并继续执行其他不依赖于输入/输出操作结果的指令。

一些粗略的比较，仅供参考：

| 硬件            | 延迟时间      | 传输速度              |
| ------------- | --------- | ----------------- |
| 中央处理器（三级缓存）   | 10 纳秒     | 400 GB/s          |
| 内存            | 100 纳秒    | 100 GB/s          |
| 硬盘（NVMe 固态硬盘） | 400 纳秒    | 380 MB/s          |
| 网络            | 50-200 毫秒 | 1 Gb/s (125 MB/s) |

(由 fio 报告的 2KB 随机读取的实际硬盘统计数据 - \~190k IOPS）

幸运的是，固态硬盘驱动器可以并发执行事务，因此 CPU 可以同时启动多个请求继续执行，然后同时接收多个事务的结果。

某些数据库（如：lmdb/mdbx）使用内存映射存储来读写磁盘，遗憾的是，内存映射存储是由内核（mmap）实现的，而不是异步的，因此在等待事务完成时会阻碍执行。

有关 Asynchronous I/O 的更多信息，请点击[此处](https://en.wikipedia.org/wiki/Asynchronous_I/O)。
