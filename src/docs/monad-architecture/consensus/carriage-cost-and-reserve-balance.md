# 传输成本和储备余额

### **方法与动机**

延迟执行的功能非常强大，因为它允许执行和共识并行作业，从而大大增加了执行的时间预算。

一个显而易见的反对意见是：既然共识节点没有最新的状态视图，那么如何防止它们误将已消耗掉所有 Gas的账户的交易打包进区块，这将会产生 DoS 攻击。

为了防止这种情况发生，Monad 为区块链上的交易传输设置了成本（即"传输成本"）。每个账户将保留一个储备余额，在交易达成共识时更新，并根据储备余额收取传输成本。

### **传输成本**

在 Monad 中，通过区块网络进行交易需要支付费用（"传输成本"），这是一项独立于执行成本的费用。

传输成本是防止垃圾交易所必需存在的，费用极低，但反映了利用网络资源的成本。

交易有可能被纳入共识（并被收取传输成本），但相对于指定的 Gas 限额，假设执行预算不足，在这种情况下，交易在执行时会失败，但仍会收取失败交易的 Gas 费用。请注意，这与以太坊并无不同：当账户中 ETH 不足时提交交易，将耗尽ETH并失败。为防止执行交易时出现失败，用户有必要在交易前为账户充值足够多的 Gas。

### **储备余额**

对于每个账户，节点保持同步两个余额：

* **储备余额**，用于支付传输成本
* **执行余额**，用于支付交易执行费用

当交易被纳入区块（达成共识）时，传输成本从储备余额中扣除；当交易执行时，费用从执行余额中扣除（双重费用）；在 `D` 个区块延迟（10 秒）后，对储备余额中的传输成本进行递减操作。

储备余额实际上是"待确认"交易的费用预算，它的存在是为了确保区块中只打包了已支付交易费用的交易。你可以认为储备余额是实时递减的（即在达成共识时），虽然节点对全局状态的洞察是滞后的，但储备余额总是反映最新的交易费用支出。

### **预留储备余额**

预留储备余额是每个账户已设定的参数，默认值预计为传输成本的较大倍数（200 倍），这样用户就可以顺利提交大量待确认交易。

如果用户计划从同一 EOA 账户发送大量待确认交易，则可通过与嵌入式智能合约交互来更改预留储备余额。预留储备余额的更改被视为执行，即只有在延迟期过后才会反映在储备余额中。
