# 延迟执行

Monad 区块链的一个新颖之处在于执行与共识分离，采用流水线式共识执行分级机制。

简而言之，共识是 Monad 节点就交易的官方排序达成一致的过程，而执行则是实际执行这些交易并更新状态的过程。

在 Monad 共识中，节点就交易的官方排序达成一致，但领导人节点或验证节点都无需执行这些交易。

也就是说，领导人在不知道结果状态根的情况下提出排序，验证节点投票区块的有效性，可以不知道区块中的所有交易执行是否可以回滚。

怎么会这样？为什么 Monad 会这样做？

这个答案是 Monad 设计的基石，它使 Monad 能够大幅提升交易速度，让单体分片区块链扩展到数百万用户。

### **交错执行和共识效率低下问题**

在以太坊中，执行是达成共识的先决条件。因此当节点就一个区块达成共识时，它们先要就以下两点达成共识：(1) 该区块中的交易列表；(2) 归集执行该交易列表后所有状态的 merkle 根。因此领导人在共享提议之前必须执行提议区块中的所有交易，而验证节点在响应投票之前也必须执行这些交易。

在这种模式下，执行的时间预算极为有限，因为它必须执行两次，并留出足够的时间进行多轮跨全局通信以达成共识。另外由于执行过程会限制共识达成，因此必须极其保守地选择 Gas 限制，以确保即使在最极端的情况下，所有节点上的计算都能在预算范围内完成。

### **确定的排序意味着状态确定性**

这里有一个显而易见却又至关重要的见解：给定一个官方的交易排序，正确的状态就完全确定了，需要执行才能揭示真相，但真相已经确定。

Monad 利用了这一见解，取消了节点在达成共识前执行交易的要求。节点协议仅是关于官方排序，每个节点独立执行区块 `N` 中的交易，同时开始就区块 `N+1` 达成共识。

这样就可以获得与整个区块时间相对应的 Gas 预算，因为执行过程只需紧跟共识即可。此外，这种方法对精确计算时间变化的容忍度更高，普遍而言，执行只需紧跟共识即可。

### **延迟merkle根仍能确保状态机复制**

人们可能对上述观点提出的主要反对意见是：

* 如果其中一个节点是恶意的，没有执行共识中指定的确定性交易，会发生什么情况？(例如，它忽略了某些交易，或者只是将状态变量设置为自己选择的任意值）。
* 如果其中一个节点在执行过程中出错，会发生什么情况？

为了解决以上问题，在 Monad 中，区块提议包含有一个延迟了 `D` 个区块的 merkle 根，其中 `D` 是一个全局参数（目前预计为 10）。因为这种延迟的 merkle 根：

1. 在网络就区块 `N` 达成共识（2/3 的多数票）后，意味着网络已同意区块 `N-D` 的官方结果处于遵循 merkle 根 `M` 的状态，此时轻客户端可以检索全节点，以获得区块 `N-D` 的状态变量值的 merkle 证明。
2. 在区块 `N-D` 执行中出现错误的任何节点将从区块 `N` 开始脱离共识层，这将触发该类节点回滚到区块 `N-D-1` 的结束状态，重新执行区块 `N-D` 中的交易（希望能实现 merkle 根匹配），接着重新执行 `N-D+1` 、`N-D+2` 等区块中的交易。

以太坊的方法是使用共识，以非常严格的方式执行状态机复制：在节点达成共识后，我们知道绝大多数认同官方排序和由该排序产生的状态结果。然而这种严格的方法带来了巨大的代价——极其有限的吞吐量，Monad 在此处稍微放宽了，取得了很好的效果。

### **最终确定性**

在 MonadBFT 中，最终确定性采用单时隙确定性（时间为 1 秒），对于使用全节点的人来说，执行结果一般会滞后不到 1 秒。让我们来解读一下这一点：

**Monad 中的最终确定性采用单时隙确定性（时间为 1 秒）**，如果你提交了一笔交易，你将在一个区块后看到该交易的官方排序（在所有其他交易中）。除非网络中的绝大多数人采取恶意行为，否则不存在回滚重新排序的可能性，这使得 Monad 的最终确定性速度远快于以太坊（2 个 epochs，时间为 12.8 分钟）。

**交易的执行结果**（成功还是失败？交易后的余额是多少？）通常会**在全节点上延迟不到1秒的最终确定性**，任何需要快速了解交易结果的人（例如，想要了解交易状态的高频交易员）都可以运行全节点。Monad 会最大限度地减少全节点的运行成本，更多信息请参阅[硬件要求](broken-reference)。

任何人如果想在不运行全节点的情况下安全地查询交易结果，可以运行一个轻客户端，同时用 merkle 证明查询全节点的余额，在这种情况下，查询将滞后于 merkle 根（延迟`D=10` 个区块，即 10 秒）。请注意，目前大多数用户都是使用软件/浏览器钱包或通过区块浏览器查看区块链状态，此类查询方式均不涉及轻客户端。

有些读者可能会错误地将 merkle 根（延迟了 `D=10` 个区块）与最终确定性混为一谈，误以为最终确定性就是 10 个区块的时间。事实并非如此，官方交易排序是在 1 个区块后确定的，在此之后，如果没有绝大多数的拜占庭行为，就不会有任何更改。
