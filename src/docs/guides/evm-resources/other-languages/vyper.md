# Vyper 语言

[Vyper](https://www.quicknode.com/guides/ethereum-development/smart-contracts/how-to-write-an-ethereum-smart-contract-using-vyper) 是一种流行的 EVM 编程语言，逻辑上类似于 Solidity，语法上类似于 Python。

[Vyper文档](https://docs.vyperlang.org/en/stable/index.html) 包括 Vyper 语言的安装、语法、编码示例和编译。

我们鼓励希望获得类似 Python 体验的典型 EVM 开发人员使用 Vyper 作为编程语言，并使用采用 Python 语言构建的 [ApeWorx](https://docs.apeworx.io/ape/stable/userguides/quickstart.html) 作为测试和部署框架，ApeWorx 还允许在分析测试结果时使用典型的 Python 库，如 Pandas。

Vyper 和 ApeWorx 可与 [Jupyter](https://jupyter.org/) 配合使用，后者提供了一个使用网络浏览器的交互式环境。 有关使用 Vyper 和 Jupyter 为 EVM 开发智能合约的快速指南，请点击[此处](https://medium.com/deepyr/interacting-with-ethereum-using-web3-py-and-jupyter-notebooks-e4207afa0085)。

### **Vyper相关资源**

* [Vyper示例](https://vyper-by-example.org/)
* [Snekmate](https://github.com/pcaversaccio/snekmate)：基于 Vyper 语言的智能合约 Gas 优化模块库
* [Curve contracts](https://github.com/curvefi/curve-contract)：Vyper 语言最突出的使用实例
