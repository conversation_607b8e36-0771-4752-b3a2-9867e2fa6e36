# 网络信息

## Monad

| 姓名       | 价值                                                         |
| ---------- | ------------------------------------------------------------ |
| 网络名称   | Monad 测试网                                                 |
| 链ID       | 10143                                                        |
| 货币符号   | 星期一                                                       |
| 区块浏览器 | [https://testnet.monadexplorer.com](https://testnet.monadexplorer.com/) |

### 公共 RPC

| RPC URL                                                      | 提供者      | 速率限制                             | 批量调用限制 | 其他限制                           |
| ------------------------------------------------------------ | ----------- | ------------------------------------ | ------------ | ---------------------------------- |
| [https://testnet-rpc.monad.xyz](https://testnet-rpc.monad.xyz/) | 快速节点    | 25 个请求/1 秒                       | 100          |                                    |
| https://rpc.ankr.com/monad_testnet                           | 安克尔      | 300 个请求/10 秒12000 个请求/10 分钟 | 100          | `debug_*`不允许使用的方法          |
| [https://rpc-testnet.monadinfra.com](https://rpc-testnet.monadinfra.com/) | Monad基金会 | 20 个请求/1 秒                       | 不允许       | `eth_getLogs`和`debug_*`方法不允许 |

### 有用的

| 姓名         | 网址                                                    |
| ------------ | ------------------------------------------------------- |
| 测试网络中心 | [https://testnet.monad.xyz](https://testnet.monad.xyz/) |
| 测试网水龙头 | [https://faucet.monad.xyz](https://faucet.monad.xyz/)   |
| 生态系统目录 | https://www.monad.xyz/ecosystem                         |
| 网络可视化   | [https://gmonads.com](https://gmonads.com/)             |

### 网上的规范合约

| 姓名                    | 地址                                                         |
| ----------------------- | ------------------------------------------------------------ |
| CreateX                 | [`******************************************`](https://testnet.monadexplorer.com/address/******************************************) |
| Foundry 确定性部署器    | [`******************************************`](https://testnet.monadexplorer.com/address/0x4e59b44847b379578588920cA78FbF26c0B4956C) |
| 入口点 v0.6             | [`0x5FF137D4b0FDCD49DcA30c7CF57E578a026d2789`](https://testnet.monadexplorer.com/address/0x5FF137D4b0FDCD49DcA30c7CF57E578a026d2789) |
| 入口点 v0.7             | [`0x0000000071727De22E5E9d8BAf0edAc6f37da032`](https://testnet.monadexplorer.com/address/0x0000000071727De22E5E9d8BAf0edAc6f37da032) |
| 多路呼叫3               | [`0xcA11bde05977b3631167028862bE2a173976CA11`](https://testnet.monadexplorer.com/address/0xcA11bde05977b3631167028862bE2a173976CA11) |
| 许可证2                 | [`0x000000000022d473030f116ddee9f6b43ac78ba3`](https://testnet.monadexplorer.com/address/0x000000000022d473030f116ddee9f6b43ac78ba3) |
| 安全单例工厂            | [`0x914d7Fec6aaC8cd542e72Bca78B30650d45643d7`](https://testnet.monadexplorer.com/address/0x914d7Fec6aaC8cd542e72Bca78B30650d45643d7) |
| UniswapV2Factory        | [`0x733e88f248b742db6c14c0b1713af5ad7fdd59d0`](https://testnet.monadexplorer.com/address/0x733e88f248b742db6c14c0b1713af5ad7fdd59d0) |
| UniswapV3Factory        | [`0x961235a9020b05c44df1026d956d1f4d78014276`](https://testnet.monadexplorer.com/address/0x961235a9020b05c44df1026d956d1f4d78014276) |
| UniswapV2Router02       | [`0xfb8e1c3b833f9e67a71c859a132cf783b645e436`](https://testnet.monadexplorer.com/address/0xfb8e1c3b833f9e67a71c859a132cf783b645e436) |
| Uniswap UniversalRouter | [`0x3ae6d8a282d67893e17aa70ebffb33ee5aa65893`](https://testnet.monadexplorer.com/address/0x3ae6d8a282d67893e17aa70ebffb33ee5aa65893) |
| WrappedMonad            | [`0x760AfE86e5de5fa0Ee542fc7B7B713e1c5425701`](https://testnet.monadexplorer.com/address/0x760AfE86e5de5fa0Ee542fc7B7B713e1c5425701) |

参见：

- [Uniswap 部署](https://github.com/Uniswap/contracts/blob/bf676eed3dc31b18c70aba61dcc6b3c6e4d0028f/deployments/10143.md)

### 测试网代币（部分列表

| 姓名           | 地址                                                         |
| -------------- | ------------------------------------------------------------ |
| USDC（测试网） | [`******************************************`](https://testnet.monadexplorer.com/address/******************************************) |
| USDT（测试网） | [`******************************************`](https://testnet.monadexplorer.com/address/******************************************) |
| WBTC（测试网） | [`******************************************`](https://testnet.monadexplorer.com/address/******************************************) |
| WETH（测试网） | [`******************************************`](https://testnet.monadexplorer.com/address/******************************************) |
| WSOL（测试网） | [`******************************************`](https://testnet.monadexplorer.com/address/******************************************) |

### 网上支持的基础设施

请参阅[工具和基础设施](https://docs.monad.xyz/tooling-and-infra/)页面以获取支持测试网的提供商列表。